<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度主题一致性测试 - 侧边栏与欢迎界面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="static/css/design-system.css">
    <link rel="stylesheet" href="static/css/dark_theme.css" id="dark-theme-style" disabled>
    <link rel="stylesheet" href="static/css/theme-transitions.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: background-color var(--duration-normal) var(--ease-out),
                       color var(--duration-normal) var(--ease-out);
            overflow-x: hidden;
        }

        .test-layout {
            display: flex;
            height: 100vh;
        }

        /* === 侧边栏测试区域 === */
        .sidebar-test {
            width: 280px;
            background: var(--bg-primary);
            border-right: 1px solid var(--border-primary);
            display: flex;
            flex-direction: column;
            transition: all var(--duration-normal) var(--ease-out);
        }

        .sidebar-header-test {
            padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--bg-primary);
            min-height: 64px;
            position: relative;
        }

        .sidebar-header-test::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: var(--spacing-lg);
            right: var(--spacing-lg);
            height: 1px;
            background: linear-gradient(90deg,
                        transparent 0%,
                        var(--border-primary) 20%,
                        var(--border-primary) 80%,
                        transparent 100%);
        }

        .sidebar-header-test h2 {
            margin: 0;
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            letter-spacing: -0.025em;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .sidebar-header-test h2::before {
            content: "💬";
            font-size: 1.2em;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }

        .sidebar-controls-test {
            padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
            border-bottom: 1px solid var(--border-secondary);
            background: linear-gradient(135deg,
                        rgba(255, 255, 255, 0.7) 0%,
                        rgba(248, 249, 250, 0.5) 100%);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .sidebar-io-buttons-test {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
        }

        .sidebar-io-button-test {
            flex: 1;
            min-width: 0;
            padding: 8px 12px;
            border: 0.5px solid var(--border-primary);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-io-button-test:hover {
            background: var(--color-gray-100);
            border-color: var(--color-gray-300);
            transform: translateY(-0.5px);
        }

        .sidebar-io-button-test i {
            font-size: 12px;
            flex-shrink: 0;
        }

        /* === 欢迎界面测试区域 === */
        .welcome-test {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 40px 20px;
            color: var(--text-tertiary);
            background: var(--bg-primary);
            transition: all var(--duration-normal) var(--ease-out);
        }

        .logo-container-test {
            width: 80px;
            height: 88px;
            margin: 0 auto 25px auto;
            flex-shrink: 0;
            position: relative;
            background-color: transparent;
            border-radius: 0;
            box-shadow: none;
            overflow: visible;
        }

        .logo-test {
            width: 100%;
            height: 100%;
            display: block;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
        }

        .welcome-title-test {
            font-size: 1.6em;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
            width: 100%;
            text-align: center;
            flex-shrink: 0;
        }

        /* 🚨 发现问题：硬编码颜色 */
        .model-name-display-test {
            font-size: 1.0em;
            color: #495057; /* 硬编码颜色！ */
            margin-bottom: 18px;
            font-weight: 500;
            width: 90%;
            text-align: center;
            word-break: break-word;
            flex-shrink: 0;
        }

        .welcome-message-test {
            font-size: 1.0em;
            max-width: 90%;
            margin: 0 auto 30px auto;
            line-height: 1.6;
            text-align: center;
            flex-shrink: 0;
            color: var(--text-secondary);
        }

        .suggestion-buttons-test {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 12px;
            width: 100%;
            max-width: 450px;
            margin: 0 auto;
            flex-shrink: 0;
        }

        .suggestion-button-test {
            background-color: var(--bg-surface);
            border: 1px solid var(--border-secondary);
            color: var(--text-secondary);
            padding: 8px 18px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all var(--duration-fast) var(--ease-out);
            flex-basis: 30%;
            min-width: 120px;
            text-align: center;
        }

        .suggestion-button-test:hover {
            background-color: var(--bg-hover);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        /* === 主题切换控制 === */
        .theme-control {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .theme-toggle-btn {
            background: var(--color-primary);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-size: 16px;
            transition: all var(--duration-fast) var(--ease-out);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .theme-toggle-btn:hover {
            background: var(--color-primary-hover);
            transform: translateY(-1px);
        }

        /* === 问题标记 === */
        .problem-marker {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* === 测试信息面板 === */
        .test-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: var(--bg-surface);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: 15px;
            max-width: 300px;
            box-shadow: var(--shadow-lg);
        }

        .test-info h3 {
            margin: 0 0 10px 0;
            color: var(--color-primary);
            font-size: 14px;
        }

        .test-info ul {
            margin: 0;
            padding-left: 20px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .test-info li {
            margin-bottom: 5px;
        }

        .problem {
            color: #ef4444;
            font-weight: 600;
        }

        .ok {
            color: #10b981;
            font-weight: 600;
        }

        /* === 尺寸测量辅助 === */
        .size-indicator {
            position: absolute;
            background: rgba(59, 130, 246, 0.1);
            border: 1px dashed #3b82f6;
            pointer-events: none;
            font-size: 10px;
            color: #3b82f6;
            padding: 2px 4px;
            border-radius: 2px;
        }

        /* === 响应式测试 === */
        @media (max-width: 768px) {
            .test-layout {
                flex-direction: column;
            }
            
            .sidebar-test {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid var(--border-primary);
            }
            
            .sidebar-io-buttons-test {
                flex-direction: column;
            }
            
            .sidebar-io-button-test {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="theme-control">
        <button class="theme-toggle-btn" onclick="toggleTheme()">
            <i class="fas fa-sun" id="theme-icon"></i>
            <span id="theme-text">切换到暗黑模式</span>
        </button>
    </div>

    <div class="test-layout">
        <!-- 侧边栏测试区域 -->
        <div class="sidebar-test">
            <div class="sidebar-header-test">
                <h2>LuckyStar AI</h2>
                <button style="background: none; border: none; font-size: 1.4em; color: var(--text-tertiary); cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="sidebar-controls-test">
                <div class="sidebar-io-buttons-test">
                    <button class="sidebar-io-button-test">
                        <i class="fas fa-download"></i>
                        <span>导入</span>
                    </button>
                    <button class="sidebar-io-button-test">
                        <i class="fas fa-upload"></i>
                        <span>导出</span>
                    </button>
                    <button class="sidebar-io-button-test">
                        <i class="fas fa-plus"></i>
                        <span>新建</span>
                    </button>
                </div>
                
                <button class="sidebar-io-button-test" style="width: 100%; margin-top: 8px;">
                    <i class="fas fa-file-alt"></i>
                    <span>查看后端日志</span>
                </button>
            </div>

            <div style="flex: 1; padding: var(--spacing-md); overflow-y: auto;">
                <div style="color: var(--text-secondary); font-size: 12px; margin-bottom: 10px;">
                    会话列表区域
                </div>
                <div style="background: var(--bg-surface); border: 1px solid var(--border-primary); border-radius: 6px; padding: 10px; margin-bottom: 8px;">
                    <div style="color: var(--text-primary); font-size: 13px;">示例会话 1</div>
                    <div style="color: var(--text-tertiary); font-size: 11px; margin-top: 4px;">2分钟前</div>
                </div>
                <div style="background: var(--bg-surface); border: 1px solid var(--border-primary); border-radius: 6px; padding: 10px;">
                    <div style="color: var(--text-primary); font-size: 13px;">示例会话 2</div>
                    <div style="color: var(--text-tertiary); font-size: 11px; margin-top: 4px;">1小时前</div>
                </div>
            </div>
        </div>

        <!-- 欢迎界面测试区域 -->
        <div class="welcome-test">
            <div class="logo-container-test">
                <div class="logo-test">🤖</div>
            </div>
            
            <div class="welcome-title-test">欢迎使用 LuckyStar AI</div>
            
            <div class="model-name-display-test" style="position: relative;">
                当前模型：Claude Sonnet 4
                <div class="problem-marker">!</div>
            </div>
            
            <div class="welcome-message-test">
                我是您的AI助手，可以帮助您解答问题、处理文档、编写代码等。请选择下面的建议开始对话，或直接输入您的问题。
            </div>
            
            <div class="suggestion-buttons-test">
                <button class="suggestion-button-test">💡 创意写作</button>
                <button class="suggestion-button-test">📊 数据分析</button>
                <button class="suggestion-button-test">💻 编程助手</button>
                <button class="suggestion-button-test">📝 文档处理</button>
                <button class="suggestion-button-test">🎨 设计建议</button>
                <button class="suggestion-button-test">🔍 信息搜索</button>
            </div>
        </div>
    </div>

    <div class="test-info">
        <h3>🔍 检测到的问题</h3>
        <ul>
            <li class="problem">模型名称显示使用硬编码颜色 #495057</li>
            <li class="ok">侧边栏按钮尺寸保持一致</li>
            <li class="ok">欢迎界面布局稳定</li>
            <li class="ok">主题切换过渡流畅</li>
        </ul>
        
        <h3 style="margin-top: 15px;">📏 测试重点</h3>
        <ul>
            <li>切换主题时观察元素是否变形</li>
            <li>检查颜色是否正确适配</li>
            <li>验证布局是否保持稳定</li>
            <li>确认文字对比度是否充足</li>
        </ul>
    </div>

    <script>
        let currentTheme = 'light';

        function toggleTheme() {
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(newTheme);
        }

        function applyTheme(theme) {
            currentTheme = theme;
            const darkThemeLink = document.getElementById('dark-theme-style');
            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');

            if (theme === 'dark') {
                document.body.classList.add('dark-theme');
                darkThemeLink.removeAttribute('disabled');
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = '切换到明亮模式';
            } else {
                document.body.classList.remove('dark-theme');
                darkThemeLink.setAttribute('disabled', 'true');
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = '切换到暗黑模式';
            }

            localStorage.setItem('themePreference', theme);
        }

        // 页面加载时恢复主题设置
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('themePreference') || 'light';
            applyTheme(savedTheme);
            
            // 添加尺寸监控
            monitorElementSizes();
        });

        // 监控元素尺寸变化
        function monitorElementSizes() {
            const elementsToMonitor = [
                '.sidebar-io-button-test',
                '.suggestion-button-test',
                '.model-name-display-test'
            ];

            elementsToMonitor.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    const observer = new ResizeObserver(entries => {
                        entries.forEach(entry => {
                            console.log(`${selector} 尺寸变化:`, {
                                width: entry.contentRect.width,
                                height: entry.contentRect.height
                            });
                        });
                    });
                    observer.observe(element);
                });
            });
        }

        // 主题切换时的布局稳定性检查
        function checkLayoutStability() {
            const testElements = document.querySelectorAll('.sidebar-io-button-test, .suggestion-button-test');
            const initialPositions = new Map();
            
            // 记录初始位置
            testElements.forEach(element => {
                const rect = element.getBoundingClientRect();
                initialPositions.set(element, {
                    x: rect.x,
                    y: rect.y,
                    width: rect.width,
                    height: rect.height
                });
            });
            
            // 切换主题后检查
            setTimeout(() => {
                testElements.forEach(element => {
                    const rect = element.getBoundingClientRect();
                    const initial = initialPositions.get(element);
                    
                    if (Math.abs(rect.width - initial.width) > 1 || 
                        Math.abs(rect.height - initial.height) > 1) {
                        console.warn('检测到元素尺寸变化:', element, {
                            initial: initial,
                            current: {
                                width: rect.width,
                                height: rect.height
                            }
                        });
                    }
                });
            }, 300);
        }

        // 在主题切换时检查布局稳定性
        const originalApplyTheme = applyTheme;
        applyTheme = function(theme) {
            checkLayoutStability();
            originalApplyTheme(theme);
        };
    </script>
</body>
</html>
