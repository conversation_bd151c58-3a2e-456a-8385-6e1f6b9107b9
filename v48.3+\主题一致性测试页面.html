<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题一致性测试页面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="static/css/design-system.css">
    <link rel="stylesheet" href="static/css/dark_theme.css" id="dark-theme-style" disabled>
    <link rel="stylesheet" href="static/css/theme-transitions.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: background-color var(--duration-normal) var(--ease-out),
                       color var(--duration-normal) var(--ease-out);
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-surface);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
        }

        .theme-toggle-btn {
            background: var(--color-primary);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-size: 16px;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .theme-toggle-btn:hover {
            background: var(--color-primary-hover);
            transform: translateY(-1px);
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-card {
            background: var(--bg-surface);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: 20px;
            box-shadow: var(--shadow-sm);
            transition: all var(--duration-normal) var(--ease-out);
        }

        .test-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .test-card h3 {
            margin: 0 0 15px 0;
            color: var(--color-primary);
            font-size: 18px;
            font-weight: 600;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .color-item {
            text-align: center;
            padding: 10px;
            border-radius: var(--radius-md);
            font-size: 12px;
            font-weight: 500;
        }

        .color-primary { background: var(--color-primary); color: white; }
        .color-success { background: var(--color-success); color: white; }
        .color-warning { background: var(--color-warning); color: white; }
        .color-danger { background: var(--color-danger); color: white; }

        .text-samples {
            margin: 15px 0;
        }

        .text-primary { color: var(--text-primary); }
        .text-secondary { color: var(--text-secondary); }
        .text-tertiary { color: var(--text-tertiary); }

        .button-samples {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .btn-primary {
            background: var(--color-primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--color-primary-hover);
        }

        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-primary);
        }

        .btn-secondary:hover {
            background: var(--bg-secondary);
        }

        .input-samples {
            margin: 15px 0;
        }

        .input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--input-bg);
            color: var(--text-primary);
            font-size: 14px;
            transition: all var(--duration-fast) var(--ease-out);
            margin-bottom: 10px;
        }

        .input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .icon-samples {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            font-size: 20px;
        }

        .icon-primary { color: var(--color-primary); }
        .icon-success { color: var(--color-success); }
        .icon-warning { color: var(--color-warning); }
        .icon-danger { color: var(--color-danger); }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-surface);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: 10px 15px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: var(--shadow-md);
            z-index: 1000;
        }

        .animation-test {
            margin: 15px 0;
        }

        .animated-box {
            width: 100px;
            height: 50px;
            background: var(--color-primary);
            border-radius: var(--radius-md);
            margin: 10px 0;
            transition: all var(--duration-normal) var(--ease-out);
            cursor: pointer;
        }

        .animated-box:hover {
            transform: scale(1.05);
            background: var(--color-primary-hover);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        .theme-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .comparison-item {
            padding: 15px;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            background: var(--bg-secondary);
        }

        .comparison-item h4 {
            margin: 0 0 10px 0;
            color: var(--color-primary);
        }

        .highlight-test {
            background: var(--color-warning);
            color: var(--text-inverse);
            padding: 5px 10px;
            border-radius: var(--radius-sm);
            display: inline-block;
            margin: 5px;
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .theme-comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <div>
                <h1>🎨 主题一致性测试页面</h1>
                <p>测试白天/黑夜主题切换的视觉一致性和功能完整性</p>
            </div>
            <button class="theme-toggle-btn" onclick="toggleTheme()">
                <i class="fas fa-sun" id="theme-icon"></i>
                <span id="theme-text">切换到暗黑模式</span>
            </button>
        </div>

        <div class="status-indicator">
            当前主题: <span id="current-theme">明亮模式</span>
        </div>

        <div class="test-grid">
            <!-- 颜色系统测试 -->
            <div class="test-card">
                <h3>🎨 颜色系统测试</h3>
                <div class="color-palette">
                    <div class="color-item color-primary">主色调</div>
                    <div class="color-item color-success">成功色</div>
                    <div class="color-item color-warning">警告色</div>
                    <div class="color-item color-danger">危险色</div>
                </div>
                <p>测试主要颜色在两个主题下的表现和对比度</p>
            </div>

            <!-- 文字系统测试 -->
            <div class="test-card">
                <h3>📝 文字系统测试</h3>
                <div class="text-samples">
                    <p class="text-primary">主要文字 - 最高对比度</p>
                    <p class="text-secondary">次要文字 - 中等对比度</p>
                    <p class="text-tertiary">辅助文字 - 较低对比度</p>
                </div>
                <p>验证文字在不同主题下的可读性和层次感</p>
            </div>

            <!-- 按钮系统测试 -->
            <div class="test-card">
                <h3>🔘 按钮系统测试</h3>
                <div class="button-samples">
                    <button class="btn btn-primary">主要按钮</button>
                    <button class="btn btn-secondary">次要按钮</button>
                </div>
                <p>测试按钮在两个主题下的视觉效果和交互反馈</p>
            </div>

            <!-- 输入框系统测试 -->
            <div class="test-card">
                <h3>📝 输入框系统测试</h3>
                <div class="input-samples">
                    <input type="text" class="input" placeholder="普通输入框">
                    <input type="text" class="input" value="已填写内容的输入框">
                </div>
                <p>验证输入框在不同主题下的边框、背景和聚焦效果</p>
            </div>

            <!-- 图标系统测试 -->
            <div class="test-card">
                <h3>🎯 图标系统测试</h3>
                <div class="icon-samples">
                    <i class="fas fa-home icon-primary" title="主色调图标"></i>
                    <i class="fas fa-check icon-success" title="成功图标"></i>
                    <i class="fas fa-exclamation-triangle icon-warning" title="警告图标"></i>
                    <i class="fas fa-times icon-danger" title="危险图标"></i>
                </div>
                <p>测试图标颜色在主题切换时的适配效果</p>
            </div>

            <!-- 动画效果测试 -->
            <div class="test-card">
                <h3>✨ 动画效果测试</h3>
                <div class="animation-test">
                    <div class="animated-box pulse-animation"></div>
                    <p>悬停上方色块测试动画效果</p>
                </div>
                <p>验证动画在主题切换时的流畅性和一致性</p>
            </div>
        </div>

        <!-- 主题对比测试 -->
        <div class="test-card">
            <h3>🔄 主题切换对比测试</h3>
            <div class="theme-comparison">
                <div class="comparison-item">
                    <h4>视觉一致性检查</h4>
                    <p>✅ 颜色方案协调统一</p>
                    <p>✅ 文字对比度充足</p>
                    <p>✅ UI元素层次清晰</p>
                </div>
                <div class="comparison-item">
                    <h4>功能一致性检查</h4>
                    <p>✅ 主题切换即时生效</p>
                    <p>✅ 状态保存正常</p>
                    <p>✅ 交互反馈统一</p>
                </div>
            </div>
            <div class="highlight-test">高亮测试元素</div>
            <div class="highlight-test">另一个高亮元素</div>
        </div>
    </div>

    <script>
        let currentTheme = 'light';

        function toggleTheme() {
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(newTheme);
        }

        function applyTheme(theme) {
            currentTheme = theme;
            const darkThemeLink = document.getElementById('dark-theme-style');
            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');
            const currentThemeSpan = document.getElementById('current-theme');

            if (theme === 'dark') {
                document.body.classList.add('dark-theme');
                darkThemeLink.removeAttribute('disabled');
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = '切换到明亮模式';
                currentThemeSpan.textContent = '暗黑模式';
            } else {
                document.body.classList.remove('dark-theme');
                darkThemeLink.setAttribute('disabled', 'true');
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = '切换到暗黑模式';
                currentThemeSpan.textContent = '明亮模式';
            }

            // 保存主题设置
            localStorage.setItem('themePreference', theme);
        }

        // 页面加载时恢复主题设置
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('themePreference') || 'light';
            applyTheme(savedTheme);
        });

        // 添加一些交互效果
        document.querySelectorAll('.test-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-2px)';
            });
        });
    </script>
</body>
</html>
