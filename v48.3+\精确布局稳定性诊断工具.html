<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精确布局稳定性诊断工具</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="static/css/design-system.css">
    <link rel="stylesheet" href="static/css/dark_theme.css" id="dark-theme-style" disabled>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: background-color var(--duration-normal) var(--ease-out),
                       color var(--duration-normal) var(--ease-out);
        }

        .diagnostic-layout {
            display: flex;
            height: 100vh;
            position: relative;
        }

        /* === 侧边栏测试区域 === */
        .sidebar-diagnostic {
            width: 280px;
            background: var(--bg-primary);
            border-right: 1px solid var(--border-primary);
            display: flex;
            flex-direction: column;
            transition: all var(--duration-normal) var(--ease-out);
            position: relative;
        }

        .sidebar-header-diagnostic {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-secondary);
            background: var(--bg-primary);
            transition: all var(--duration-normal) var(--ease-out);
        }

        .sidebar-controls-diagnostic {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-secondary);
            background: var(--bg-primary);
            transition: all var(--duration-normal) var(--ease-out);
        }

        .sidebar-io-buttons-diagnostic {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
            margin-bottom: var(--spacing-md);
        }

        .sidebar-io-button-diagnostic {
            flex: 1;
            min-width: 0;
            padding: 8px 12px;
            border: 0.5px solid var(--border-primary);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
        }

        .sidebar-io-button-diagnostic:hover {
            background: var(--color-gray-100);
            border-color: var(--color-gray-300);
            transform: translateY(-0.5px);
        }

        /* === 欢迎界面测试区域 === */
        .welcome-diagnostic {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 40px 20px;
            background: var(--bg-primary);
            transition: all var(--duration-normal) var(--ease-out);
            position: relative;
        }

        .welcome-title-diagnostic {
            font-size: 1.6em;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
            transition: color var(--duration-normal) var(--ease-out);
            position: relative;
        }

        .model-name-display-diagnostic {
            font-size: 1.0em;
            color: var(--text-secondary);
            margin-bottom: 18px;
            font-weight: 500;
            transition: color var(--duration-normal) var(--ease-out);
            position: relative;
        }

        .suggestion-buttons-diagnostic {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 12px;
            width: 100%;
            max-width: 450px;
            margin: 0 auto;
        }

        .suggestion-button-diagnostic {
            background-color: var(--bg-surface);
            border: 1px solid var(--border-secondary);
            color: var(--text-secondary);
            padding: 8px 18px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all var(--duration-fast) var(--ease-out);
            flex-basis: 30%;
            min-width: 120px;
            text-align: center;
            position: relative;
        }

        /* === 诊断工具界面 === */
        .diagnostic-panel {
            position: fixed;
            top: 0;
            right: 0;
            width: 350px;
            height: 100vh;
            background: var(--bg-surface);
            border-left: 2px solid var(--border-primary);
            padding: 20px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
        }

        .diagnostic-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--border-primary);
        }

        .diagnostic-header h2 {
            margin: 0;
            color: var(--color-primary);
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-section {
            margin-bottom: 25px;
            padding: 15px;
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-secondary);
        }

        .control-section h3 {
            margin: 0 0 15px 0;
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
        }

        .theme-toggle-diagnostic {
            width: 100%;
            padding: 12px;
            background: var(--color-primary);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all var(--duration-fast) var(--ease-out);
            margin-bottom: 10px;
        }

        .theme-toggle-diagnostic:hover {
            background: var(--color-primary-hover);
            transform: translateY(-1px);
        }

        .measurement-display {
            background: var(--bg-primary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-md);
            padding: 10px;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }

        .measurement-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 3px 0;
        }

        .measurement-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .measurement-value {
            color: var(--text-primary);
            font-weight: 600;
        }

        .change-indicator {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
            margin-left: 5px;
        }

        .change-indicator.stable {
            background: #dcfce7;
            color: #166534;
        }

        .change-indicator.changed {
            background: #fef2f2;
            color: #991b1b;
            animation: pulse 1s infinite;
        }

        .element-tracker {
            border: 2px dashed transparent;
            transition: border-color 0.3s ease;
        }

        .element-tracker.measuring {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.05);
        }

        .position-marker {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ef4444;
            border-radius: 50%;
            pointer-events: none;
            z-index: 999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .position-marker.visible {
            opacity: 1;
        }

        .measurement-log {
            max-height: 200px;
            overflow-y: auto;
            background: var(--bg-primary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-md);
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 10px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 3px 0;
            border-bottom: 1px solid var(--border-tertiary);
        }

        .log-timestamp {
            color: var(--text-tertiary);
            font-size: 9px;
        }

        .log-change {
            color: var(--color-danger);
            font-weight: 600;
        }

        .log-stable {
            color: var(--color-success);
        }

        /* === 优化后的主题切换指示器 === */
        .theme-switch-indicator-optimized {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-overlay);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-full);
            padding: 8px 16px;
            z-index: 999;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(8px);
            font-size: 13px;
        }

        .theme-switch-indicator-optimized.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .theme-switch-indicator-optimized i {
            font-size: 14px;
            color: var(--color-primary);
        }

        .theme-switch-indicator-optimized span {
            color: var(--text-primary);
            font-weight: 500;
        }

        /* 响应式适配 */
        @media (max-width: 768px) {
            .diagnostic-panel {
                width: 100%;
                height: 50vh;
                top: auto;
                bottom: 0;
                right: 0;
            }
            
            .theme-switch-indicator-optimized {
                top: 10px;
                right: 10px;
                padding: 6px 12px;
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .theme-switch-indicator-optimized {
                left: 50%;
                right: auto;
                transform: translateX(-50%) translateY(-10px);
            }
            
            .theme-switch-indicator-optimized.visible {
                transform: translateX(-50%) translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="diagnostic-layout">
        <!-- 侧边栏测试区域 -->
        <div class="sidebar-diagnostic">
            <div class="sidebar-header-diagnostic element-tracker" data-element="sidebar-header">
                <h2>LuckyStar AI</h2>
            </div>

            <div class="sidebar-controls-diagnostic element-tracker" data-element="sidebar-controls">
                <div class="sidebar-io-buttons-diagnostic">
                    <button class="sidebar-io-button-diagnostic element-tracker" data-element="import-btn">
                        <i class="fas fa-download"></i>
                        <span>导入</span>
                    </button>
                    <button class="sidebar-io-button-diagnostic element-tracker" data-element="export-btn">
                        <i class="fas fa-upload"></i>
                        <span>导出</span>
                    </button>
                    <button class="sidebar-io-button-diagnostic element-tracker" data-element="new-btn">
                        <i class="fas fa-plus"></i>
                        <span>新建</span>
                    </button>
                </div>
                
                <button class="sidebar-io-button-diagnostic element-tracker" data-element="log-btn" style="width: 100%;">
                    <i class="fas fa-file-alt"></i>
                    <span>查看后端日志</span>
                </button>
            </div>
        </div>

        <!-- 欢迎界面测试区域 -->
        <div class="welcome-diagnostic">
            <div class="welcome-title-diagnostic element-tracker" data-element="welcome-title">
                欢迎使用 LuckyStar AI
            </div>
            
            <div class="model-name-display-diagnostic element-tracker" data-element="model-name">
                当前模型：Claude Sonnet 4
            </div>
            
            <div class="suggestion-buttons-diagnostic">
                <button class="suggestion-button-diagnostic element-tracker" data-element="btn-1">💡 创意写作</button>
                <button class="suggestion-button-diagnostic element-tracker" data-element="btn-2">📊 数据分析</button>
                <button class="suggestion-button-diagnostic element-tracker" data-element="btn-3">💻 编程助手</button>
                <button class="suggestion-button-diagnostic element-tracker" data-element="btn-4">📝 文档处理</button>
                <button class="suggestion-button-diagnostic element-tracker" data-element="btn-5">🎨 设计建议</button>
                <button class="suggestion-button-diagnostic element-tracker" data-element="btn-6">🔍 信息搜索</button>
            </div>
        </div>
    </div>

    <!-- 诊断工具面板 -->
    <div class="diagnostic-panel">
        <div class="diagnostic-header">
            <h2>
                <i class="fas fa-microscope"></i>
                布局稳定性诊断
            </h2>
        </div>

        <div class="control-section">
            <h3>🎛️ 控制面板</h3>
            <button class="theme-toggle-diagnostic" onclick="toggleThemeWithDiagnostic()">
                <i class="fas fa-sun" id="theme-icon"></i>
                <span id="theme-text">切换到暗黑模式</span>
            </button>
            <button class="theme-toggle-diagnostic" onclick="startContinuousMeasurement()" style="background: var(--color-success);">
                <i class="fas fa-play"></i>
                开始连续测量
            </button>
            <button class="theme-toggle-diagnostic" onclick="clearMeasurementLog()" style="background: var(--color-warning);">
                <i class="fas fa-trash"></i>
                清除日志
            </button>
        </div>

        <div class="control-section">
            <h3>📏 实时测量数据</h3>
            <div id="measurement-display"></div>
        </div>

        <div class="control-section">
            <h3>📊 变化日志</h3>
            <div class="measurement-log" id="measurement-log"></div>
        </div>

        <div class="control-section">
            <h3>🎯 检测统计</h3>
            <div class="measurement-display">
                <div class="measurement-item">
                    <span class="measurement-label">总测量次数:</span>
                    <span class="measurement-value" id="total-measurements">0</span>
                </div>
                <div class="measurement-item">
                    <span class="measurement-label">检测到变化:</span>
                    <span class="measurement-value" id="detected-changes">0</span>
                </div>
                <div class="measurement-item">
                    <span class="measurement-label">稳定性评分:</span>
                    <span class="measurement-value" id="stability-score">100%</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentTheme = 'light';
        let measurementData = new Map();
        let measurementCount = 0;
        let changeCount = 0;
        let continuousMeasurement = false;

        // 要监控的元素列表
        const elementsToTrack = [
            'sidebar-header',
            'sidebar-controls', 
            'import-btn',
            'export-btn',
            'new-btn',
            'log-btn',
            'welcome-title',
            'model-name',
            'btn-1',
            'btn-2',
            'btn-3',
            'btn-4',
            'btn-5',
            'btn-6'
        ];

        function measureElement(elementId) {
            const element = document.querySelector(`[data-element="${elementId}"]`);
            if (!element) return null;

            const rect = element.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(element);
            
            return {
                x: Math.round(rect.x * 100) / 100,
                y: Math.round(rect.y * 100) / 100,
                width: Math.round(rect.width * 100) / 100,
                height: Math.round(rect.height * 100) / 100,
                padding: computedStyle.padding,
                margin: computedStyle.margin,
                fontSize: computedStyle.fontSize,
                lineHeight: computedStyle.lineHeight,
                borderWidth: computedStyle.borderWidth
            };
        }

        function measureAllElements() {
            const measurements = {};
            elementsToTrack.forEach(elementId => {
                measurements[elementId] = measureElement(elementId);
            });
            return measurements;
        }

        function comparemeasurements(before, after) {
            const changes = {};
            const tolerance = 0.5; // 0.5px 容差

            for (const elementId in before) {
                if (!after[elementId]) continue;
                
                const beforeData = before[elementId];
                const afterData = after[elementId];
                const elementChanges = {};

                // 检查位置和尺寸变化
                ['x', 'y', 'width', 'height'].forEach(prop => {
                    const diff = Math.abs(beforeData[prop] - afterData[prop]);
                    if (diff > tolerance) {
                        elementChanges[prop] = {
                            before: beforeData[prop],
                            after: afterData[prop],
                            diff: Math.round(diff * 100) / 100
                        };
                    }
                });

                // 检查样式变化
                ['padding', 'margin', 'fontSize', 'lineHeight', 'borderWidth'].forEach(prop => {
                    if (beforeData[prop] !== afterData[prop]) {
                        elementChanges[prop] = {
                            before: beforeData[prop],
                            after: afterData[prop]
                        };
                    }
                });

                if (Object.keys(elementChanges).length > 0) {
                    changes[elementId] = elementChanges;
                }
            }

            return changes;
        }

        function logMeasurement(changes, timestamp) {
            const logContainer = document.getElementById('measurement-log');
            const hasChanges = Object.keys(changes).length > 0;
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            if (hasChanges) {
                changeCount++;
                logEntry.innerHTML = `
                    <div class="log-timestamp">${timestamp}</div>
                    <div class="log-change">检测到 ${Object.keys(changes).length} 个元素发生变化:</div>
                    ${Object.entries(changes).map(([elementId, elementChanges]) => 
                        `<div style="margin-left: 10px; font-size: 9px;">
                            ${elementId}: ${Object.keys(elementChanges).join(', ')}
                        </div>`
                    ).join('')}
                `;
            } else {
                logEntry.innerHTML = `
                    <div class="log-timestamp">${timestamp}</div>
                    <div class="log-stable">✓ 所有元素布局稳定</div>
                `;
            }
            
            logContainer.insertBefore(logEntry, logContainer.firstChild);
            
            // 限制日志条目数量
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.lastChild);
            }
            
            // 更新统计
            updateStatistics();
        }

        function updateStatistics() {
            document.getElementById('total-measurements').textContent = measurementCount;
            document.getElementById('detected-changes').textContent = changeCount;
            
            const stabilityScore = measurementCount > 0 ? 
                Math.round((1 - changeCount / measurementCount) * 100) : 100;
            document.getElementById('stability-score').textContent = stabilityScore + '%';
        }

        function updateMeasurementDisplay(measurements) {
            const displayContainer = document.getElementById('measurement-display');
            displayContainer.innerHTML = '';
            
            elementsToTrack.slice(0, 6).forEach(elementId => {
                const data = measurements[elementId];
                if (!data) return;
                
                const measurementDiv = document.createElement('div');
                measurementDiv.className = 'measurement-display';
                measurementDiv.innerHTML = `
                    <div class="measurement-item">
                        <span class="measurement-label">${elementId}:</span>
                    </div>
                    <div class="measurement-item">
                        <span class="measurement-label">位置:</span>
                        <span class="measurement-value">${data.x}, ${data.y}</span>
                    </div>
                    <div class="measurement-item">
                        <span class="measurement-label">尺寸:</span>
                        <span class="measurement-value">${data.width} × ${data.height}</span>
                    </div>
                `;
                displayContainer.appendChild(measurementDiv);
            });
        }

        function toggleThemeWithDiagnostic() {
            // 测量切换前的状态
            const beforeMeasurements = measureAllElements();
            
            // 执行主题切换
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(newTheme);
            
            // 显示优化后的指示器
            showOptimizedThemeIndicator(newTheme);
            
            // 延迟测量以等待过渡完成
            setTimeout(() => {
                const afterMeasurements = measureAllElements();
                const changes = comparemeasurements(beforeMeasurements, afterMeasurements);
                
                measurementCount++;
                const timestamp = new Date().toLocaleTimeString();
                logMeasurement(changes, timestamp);
                updateMeasurementDisplay(afterMeasurements);
                
                // 高亮发生变化的元素
                highlightChangedElements(changes);
            }, 350); // 等待过渡动画完成
        }

        function highlightChangedElements(changes) {
            // 清除之前的高亮
            document.querySelectorAll('.element-tracker').forEach(el => {
                el.classList.remove('measuring');
            });
            
            // 高亮发生变化的元素
            Object.keys(changes).forEach(elementId => {
                const element = document.querySelector(`[data-element="${elementId}"]`);
                if (element) {
                    element.classList.add('measuring');
                    setTimeout(() => {
                        element.classList.remove('measuring');
                    }, 2000);
                }
            });
        }

        function applyTheme(theme) {
            currentTheme = theme;
            const darkThemeLink = document.getElementById('dark-theme-style');
            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');

            if (theme === 'dark') {
                document.body.classList.add('dark-theme');
                darkThemeLink.removeAttribute('disabled');
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = '切换到明亮模式';
            } else {
                document.body.classList.remove('dark-theme');
                darkThemeLink.setAttribute('disabled', 'true');
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = '切换到暗黑模式';
            }
        }

        function showOptimizedThemeIndicator(theme) {
            // 移除现有指示器
            const existingIndicator = document.querySelector('.theme-switch-indicator-optimized');
            if (existingIndicator) {
                existingIndicator.remove();
            }

            // 创建优化后的指示器
            const indicator = document.createElement('div');
            indicator.className = 'theme-switch-indicator-optimized';
            indicator.innerHTML = `
                <i class="fas fa-${theme === 'dark' ? 'moon' : 'sun'}"></i>
                <span>${theme === 'dark' ? '暗黑' : '明亮'}模式</span>
            `;

            document.body.appendChild(indicator);

            // 显示动画
            setTimeout(() => {
                indicator.classList.add('visible');
            }, 50);

            // 自动隐藏
            setTimeout(() => {
                indicator.classList.remove('visible');
                setTimeout(() => {
                    if (indicator.parentNode) {
                        indicator.parentNode.removeChild(indicator);
                    }
                }, 300);
            }, 2000);
        }

        function startContinuousMeasurement() {
            continuousMeasurement = !continuousMeasurement;
            const button = event.target;
            
            if (continuousMeasurement) {
                button.innerHTML = '<i class="fas fa-pause"></i> 停止连续测量';
                button.style.background = 'var(--color-danger)';
                
                const measureInterval = setInterval(() => {
                    if (!continuousMeasurement) {
                        clearInterval(measureInterval);
                        return;
                    }
                    
                    const measurements = measureAllElements();
                    updateMeasurementDisplay(measurements);
                }, 100);
            } else {
                button.innerHTML = '<i class="fas fa-play"></i> 开始连续测量';
                button.style.background = 'var(--color-success)';
            }
        }

        function clearMeasurementLog() {
            document.getElementById('measurement-log').innerHTML = '';
            measurementCount = 0;
            changeCount = 0;
            updateStatistics();
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始测量
            const initialMeasurements = measureAllElements();
            updateMeasurementDisplay(initialMeasurements);
            
            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.key === 't' && e.ctrlKey) {
                    e.preventDefault();
                    toggleThemeWithDiagnostic();
                }
            });
            
            console.log('布局稳定性诊断工具已启动');
            console.log('快捷键: Ctrl+T 切换主题');
        });
    </script>
</body>
</html>
