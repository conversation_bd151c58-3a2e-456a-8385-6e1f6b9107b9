/* ===================================================================
   布局稳定性修复 CSS
   专门用于修复主题切换时的布局不稳定问题
   ================================================================== */

/* === 核心原则 === 
   1. 避免使用 transition: all，改为具体属性过渡
   2. 确保尺寸相关属性不参与过渡动画
   3. 只对视觉属性（颜色、阴影、透明度）进行过渡
   4. 保持布局属性（width、height、padding、margin）稳定
*/

/* === 侧边栏按钮稳定性修复 === */
.sidebar-io-button {
    /* 移除可能导致尺寸变化的过渡 */
    transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
               border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
               color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
               box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    
    /* 确保尺寸稳定 */
    box-sizing: border-box;
    min-height: 32px; /* 固定最小高度 */
    
    /* 防止字体渲染差异导致的尺寸变化 */
    font-feature-settings: "kern" 1, "liga" 0;
    text-rendering: optimizeLegibility;
}

.sidebar-io-button:hover {
    /* 只使用轻微的视觉变换，避免布局影响 */
    transform: translateY(-0.5px);
}

/* === 欢迎界面建议按钮稳定性修复 === */
#empty-state-active .suggestion-button {
    /* 精确控制过渡属性 */
    transition: background-color var(--duration-fast) var(--ease-out),
               border-color var(--duration-fast) var(--ease-out),
               color var(--duration-fast) var(--ease-out),
               box-shadow var(--duration-fast) var(--ease-out) !important;
    
    /* 确保尺寸稳定 */
    box-sizing: border-box;
    min-height: 36px; /* 固定最小高度 */
    
    /* 防止文字换行导致的高度变化 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* === 主题切换指示器稳定性优化 === */
.theme-switch-indicator {
    /* 确保指示器不影响页面布局 */
    position: fixed !important;
    pointer-events: none !important;
    
    /* 优化过渡效果，避免布局跳动 */
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
               transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    
    /* 确保在所有设备上都不遮挡内容 */
    z-index: 9999 !important;
}

/* === 消息容器稳定性修复 === */
.message-container {
    /* 避免消息容器在主题切换时发生布局变化 */
    transition: background-color var(--duration-normal) var(--ease-out),
               border-color var(--duration-normal) var(--ease-out),
               color var(--duration-normal) var(--ease-out) !important;
}

/* === 输入框稳定性修复 === */
#user-input,
.form-group input,
.form-group textarea {
    /* 确保输入框尺寸稳定 */
    transition: border-color var(--duration-fast) var(--ease-out),
               background-color var(--duration-fast) var(--ease-out),
               color var(--duration-fast) var(--ease-out),
               box-shadow var(--duration-fast) var(--ease-out) !important;
    
    /* 防止字体渲染差异 */
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
}

/* === 代码块稳定性修复 === */
pre,
code,
.code-block {
    /* 代码块应该保持完全稳定的布局 */
    transition: background-color var(--duration-normal) var(--ease-out),
               border-color var(--duration-normal) var(--ease-out),
               color var(--duration-normal) var(--ease-out) !important;
    
    /* 确保代码字体渲染一致 */
    font-feature-settings: "liga" 0, "kern" 1, "tnum" 1;
    font-variant-numeric: tabular-nums;
    text-rendering: optimizeSpeed; /* 代码块优先速度 */
}

/* === 按钮通用稳定性修复 === */
button,
.button,
[role="button"] {
    /* 通用按钮稳定性规则 */
    transition: background-color var(--duration-fast) var(--ease-out),
               border-color var(--duration-fast) var(--ease-out),
               color var(--duration-fast) var(--ease-out),
               box-shadow var(--duration-fast) var(--ease-out) !important;
    
    /* 确保按钮尺寸稳定 */
    box-sizing: border-box;
    
    /* 防止文字渲染差异 */
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
}

/* === 模态框稳定性修复 === */
.modal,
.modal-content {
    /* 模态框应该保持稳定的布局 */
    transition: opacity var(--duration-base) var(--ease-out),
               transform var(--duration-base) var(--ease-out),
               background-color var(--duration-base) var(--ease-out) !important;
}

/* === 工具提示稳定性修复 === */
.tooltip,
[data-tooltip] {
    /* 工具提示不应影响布局 */
    transition: opacity var(--duration-fast) var(--ease-out),
               transform var(--duration-fast) var(--ease-out) !important;
}

/* === 图像和媒体元素稳定性 === */
img,
video,
canvas,
svg {
    /* 媒体元素应该保持尺寸稳定 */
    transition: opacity var(--duration-normal) var(--ease-out),
               filter var(--duration-normal) var(--ease-out) !important;
}

/* === 列表和表格稳定性 === */
ul, ol, li,
table, tr, td, th {
    /* 列表和表格元素保持布局稳定 */
    transition: background-color var(--duration-normal) var(--ease-out),
               border-color var(--duration-normal) var(--ease-out),
               color var(--duration-normal) var(--ease-out) !important;
}

/* === 特殊情况：完全禁用过渡的元素 === */
.no-transition,
.layout-critical {
    transition: none !important;
}

/* === 主题切换期间的临时稳定性增强 === */
body.theme-switching * {
    /* 在主题切换期间，暂时禁用可能导致布局变化的过渡 */
    transition-property: background-color, border-color, color, opacity, box-shadow !important;
    transition-duration: 0.1s !important;
}

/* === 响应式布局稳定性 === */
@media (max-width: 768px) {
    .sidebar-io-button,
    #empty-state-active .suggestion-button {
        /* 在小屏幕上确保更严格的尺寸控制 */
        min-height: 40px;
        padding: 8px 12px;
    }
    
    .theme-switch-indicator {
        /* 小屏幕上的指示器位置优化 */
        top: 10px !important;
        right: 10px !important;
        font-size: 12px !important;
    }
}

@media (max-width: 480px) {
    .theme-switch-indicator {
        /* 超小屏幕上居中显示 */
        left: 50% !important;
        right: auto !important;
        transform: translateX(-50%) translateY(-10px) !important;
    }
    
    .theme-switch-indicator.visible {
        transform: translateX(-50%) translateY(0) !important;
    }
}

/* === 高对比度模式稳定性 === */
@media (prefers-contrast: high) {
    * {
        /* 高对比度模式下禁用复杂过渡 */
        transition-duration: 0.1s !important;
    }
}

/* === 减少动画偏好支持 === */
@media (prefers-reduced-motion: reduce) {
    * {
        /* 用户偏好减少动画时的稳定性优化 */
        transition-duration: 0.01s !important;
        animation-duration: 0.01s !important;
    }
    
    .theme-switch-indicator {
        /* 简化指示器动画 */
        transition: opacity 0.1s ease !important;
        transform: none !important;
    }
}

/* === 调试辅助（开发环境使用） === */
.debug-layout-stability {
    /* 调试时高亮可能有问题的元素 */
    outline: 2px dashed rgba(255, 0, 0, 0.3) !important;
    background: rgba(255, 0, 0, 0.05) !important;
}

/* === 性能优化 === */
.gpu-accelerated {
    /* 对需要频繁变化的元素启用GPU加速 */
    transform: translateZ(0);
    will-change: transform, opacity;
}

.theme-switch-indicator,
.sidebar-io-button:hover,
#empty-state-active .suggestion-button:hover {
    /* 为关键交互元素启用GPU加速 */
    transform: translateZ(0);
    will-change: transform, opacity, background-color;
}

/* === 字体渲染优化 === */
* {
    /* 全局字体渲染优化，减少主题切换时的字体渲染差异 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-smooth: always;
    text-rendering: optimizeLegibility;
}

/* === 最终覆盖规则 === */
/* 确保关键元素的稳定性规则不被覆盖 */
.sidebar-io-button,
#empty-state-active .suggestion-button,
.theme-switch-indicator {
    /* 最高优先级的稳定性保证 */
    box-sizing: border-box !important;
    font-feature-settings: "kern" 1 !important;
}
