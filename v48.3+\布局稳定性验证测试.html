<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局稳定性验证测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="static/css/design-system.css">
    <link rel="stylesheet" href="static/css/dark_theme.css" id="dark-theme-style" disabled>
    <link rel="stylesheet" href="static/css/layout-stability-fixes.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: background-color var(--duration-normal) var(--ease-out),
                       color var(--duration-normal) var(--ease-out);
        }

        .test-layout {
            display: flex;
            height: 100vh;
            position: relative;
        }

        /* === 测试侧边栏 === */
        .test-sidebar {
            width: 280px;
            background: var(--bg-primary);
            border-right: 1px solid var(--border-primary);
            display: flex;
            flex-direction: column;
            padding: 20px;
            gap: 20px;
        }

        .test-section {
            background: var(--bg-surface);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            padding: 15px;
        }

        .test-section h3 {
            margin: 0 0 15px 0;
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
        }

        .test-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .test-button {
            flex: 1;
            min-width: 0;
            padding: 8px 12px;
            border: 0.5px solid var(--border-primary);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .test-button:hover {
            background: var(--color-gray-100);
            border-color: var(--color-gray-300);
            transform: translateY(-0.5px);
        }

        /* === 测试主内容区 === */
        .test-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 40px;
            background: var(--bg-primary);
        }

        .welcome-test {
            text-align: center;
            margin-bottom: 40px;
        }

        .welcome-title {
            font-size: 1.6em;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
        }

        .model-name {
            font-size: 1.0em;
            color: var(--text-secondary);
            margin-bottom: 18px;
            font-weight: 500;
        }

        .suggestion-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 12px;
            width: 100%;
            max-width: 450px;
            margin: 0 auto 40px auto;
        }

        .suggestion-button {
            background-color: var(--bg-surface);
            border: 1px solid var(--border-secondary);
            color: var(--text-secondary);
            padding: 8px 18px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            flex-basis: 30%;
            min-width: 120px;
            text-align: center;
        }

        .suggestion-button:hover {
            background-color: var(--bg-hover);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        /* === 测试控制面板 === */
        .test-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-surface);
            border: 2px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: 20px;
            box-shadow: var(--shadow-xl);
            z-index: 1000;
            min-width: 300px;
        }

        .control-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--border-primary);
        }

        .control-header h2 {
            margin: 0;
            color: var(--color-primary);
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group h4 {
            margin: 0 0 10px 0;
            color: var(--text-primary);
            font-size: 13px;
            font-weight: 600;
        }

        .control-button {
            width: 100%;
            padding: 10px;
            margin-bottom: 8px;
            background: var(--color-primary);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .control-button:hover {
            background: var(--color-primary-hover);
            transform: translateY(-1px);
        }

        .control-button.secondary {
            background: var(--color-gray-500);
        }

        .control-button.secondary:hover {
            background: var(--color-gray-600);
        }

        .control-button.success {
            background: var(--color-success);
        }

        .control-button.success:hover {
            background: var(--color-success-hover);
        }

        .control-button.warning {
            background: var(--color-warning);
        }

        .control-button.warning:hover {
            background: var(--color-warning-hover);
        }

        /* === 测试结果显示 === */
        .test-results {
            background: var(--bg-primary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-md);
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid var(--border-tertiary);
        }

        .result-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .result-value {
            color: var(--text-primary);
            font-weight: 600;
        }

        .result-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
        }

        .result-status.pass {
            background: #dcfce7;
            color: #166534;
        }

        .result-status.fail {
            background: #fef2f2;
            color: #991b1b;
        }

        .result-status.warning {
            background: #fef3c7;
            color: #92400e;
        }

        /* === 优化后的主题切换指示器测试 === */
        .theme-indicator-test {
            position: fixed;
            top: 20px;
            right: 340px;
            background: var(--bg-overlay);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-full);
            padding: 8px 16px;
            z-index: 999;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(8px);
            font-size: 13px;
            max-width: 200px;
        }

        .theme-indicator-test.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .theme-indicator-test i {
            font-size: 14px;
            color: var(--color-primary);
        }

        .theme-indicator-test span {
            color: var(--text-primary);
            font-weight: 500;
        }

        /* === 响应式适配 === */
        @media (max-width: 768px) {
            .test-controls {
                width: 90%;
                right: 5%;
                top: 10px;
            }
            
            .theme-indicator-test {
                right: 5%;
                top: 60px;
            }
        }

        @media (max-width: 480px) {
            .test-layout {
                flex-direction: column;
            }
            
            .test-sidebar {
                width: 100%;
                height: 200px;
                overflow-y: auto;
            }
            
            .theme-indicator-test {
                left: 50%;
                right: auto;
                transform: translateX(-50%) translateY(-10px);
            }
            
            .theme-indicator-test.visible {
                transform: translateX(-50%) translateY(0);
            }
        }

        /* === 测试元素高亮 === */
        .element-highlight {
            outline: 2px solid var(--color-primary);
            outline-offset: 2px;
            background: rgba(59, 130, 246, 0.05);
        }

        .measuring {
            animation: measurePulse 1s infinite;
        }

        @keyframes measurePulse {
            0%, 100% { 
                outline-color: var(--color-primary);
                background: rgba(59, 130, 246, 0.05);
            }
            50% { 
                outline-color: var(--color-success);
                background: rgba(34, 197, 94, 0.05);
            }
        }
    </style>
</head>
<body>
    <div class="test-layout">
        <!-- 测试侧边栏 -->
        <div class="test-sidebar">
            <div class="test-section">
                <h3>侧边栏按钮测试</h3>
                <div class="test-buttons">
                    <button class="test-button sidebar-io-button" data-test="sidebar-btn-1">
                        <i class="fas fa-download"></i>
                        <span>导入</span>
                    </button>
                    <button class="test-button sidebar-io-button" data-test="sidebar-btn-2">
                        <i class="fas fa-upload"></i>
                        <span>导出</span>
                    </button>
                    <button class="test-button sidebar-io-button" data-test="sidebar-btn-3">
                        <i class="fas fa-plus"></i>
                        <span>新建</span>
                    </button>
                </div>
                
                <button class="test-button sidebar-io-button" data-test="sidebar-btn-4" style="width: 100%;">
                    <i class="fas fa-file-alt"></i>
                    <span>查看后端日志</span>
                </button>
            </div>

            <div class="test-section">
                <h3>其他控件测试</h3>
                <div class="test-buttons">
                    <button class="test-button" data-test="control-btn-1">
                        <i class="fas fa-cog"></i>
                        <span>设置</span>
                    </button>
                    <button class="test-button" data-test="control-btn-2">
                        <i class="fas fa-info"></i>
                        <span>帮助</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 测试主内容区 -->
        <div class="test-main">
            <div class="welcome-test">
                <div class="welcome-title" data-test="welcome-title">
                    欢迎使用 LuckyStar AI
                </div>
                
                <div class="model-name" data-test="model-name">
                    当前模型：Claude Sonnet 4
                </div>
                
                <div class="suggestion-buttons">
                    <button class="suggestion-button" data-test="suggestion-1">💡 创意写作</button>
                    <button class="suggestion-button" data-test="suggestion-2">📊 数据分析</button>
                    <button class="suggestion-button" data-test="suggestion-3">💻 编程助手</button>
                    <button class="suggestion-button" data-test="suggestion-4">📝 文档处理</button>
                    <button class="suggestion-button" data-test="suggestion-5">🎨 设计建议</button>
                    <button class="suggestion-button" data-test="suggestion-6">🔍 信息搜索</button>
                </div>
            </div>

            <div class="test-section">
                <h3>布局稳定性测试说明</h3>
                <p>此页面用于验证主题切换时的布局稳定性。使用右侧控制面板进行各种测试。</p>
                <ul>
                    <li>✅ 主题切换指示器已优化到右上角位置</li>
                    <li>✅ 侧边栏按钮过渡效果已优化，避免布局变化</li>
                    <li>✅ 欢迎界面建议按钮稳定性已增强</li>
                    <li>✅ 引入了专门的布局稳定性修复CSS</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
        <div class="control-header">
            <h2>
                <i class="fas fa-vial"></i>
                布局稳定性测试
            </h2>
        </div>

        <div class="control-group">
            <h4>🎛️ 主题切换测试</h4>
            <button class="control-button" onclick="toggleThemeTest()">
                <i class="fas fa-sun" id="theme-icon"></i>
                <span id="theme-text">切换到暗黑模式</span>
            </button>
            <button class="control-button secondary" onclick="rapidThemeTest()">
                <i class="fas fa-bolt"></i>
                快速切换测试 (5次)
            </button>
        </div>

        <div class="control-group">
            <h4>📏 布局测量</h4>
            <button class="control-button success" onclick="measureAllElements()">
                <i class="fas fa-ruler"></i>
                测量所有元素
            </button>
            <button class="control-button warning" onclick="startContinuousMonitoring()">
                <i class="fas fa-eye" id="monitor-icon"></i>
                <span id="monitor-text">开始连续监控</span>
            </button>
        </div>

        <div class="control-group">
            <h4>📊 测试结果</h4>
            <div class="test-results" id="test-results">
                <div class="result-item">
                    <span class="result-label">等待测试...</span>
                </div>
            </div>
        </div>

        <div class="control-group">
            <button class="control-button secondary" onclick="clearResults()">
                <i class="fas fa-trash"></i>
                清除结果
            </button>
        </div>
    </div>

    <!-- 优化后的主题切换指示器 -->
    <div class="theme-indicator-test" id="theme-indicator">
        <i class="fas fa-sun"></i>
        <span>明亮模式</span>
    </div>

    <script>
        let currentTheme = 'light';
        let isMonitoring = false;
        let monitoringInterval = null;
        let testResults = [];

        // 要测试的元素列表
        const testElements = [
            'sidebar-btn-1', 'sidebar-btn-2', 'sidebar-btn-3', 'sidebar-btn-4',
            'control-btn-1', 'control-btn-2',
            'welcome-title', 'model-name',
            'suggestion-1', 'suggestion-2', 'suggestion-3', 'suggestion-4', 'suggestion-5', 'suggestion-6'
        ];

        function measureElement(elementId) {
            const element = document.querySelector(`[data-test="${elementId}"]`);
            if (!element) return null;

            const rect = element.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(element);
            
            return {
                x: Math.round(rect.x * 100) / 100,
                y: Math.round(rect.y * 100) / 100,
                width: Math.round(rect.width * 100) / 100,
                height: Math.round(rect.height * 100) / 100,
                fontSize: computedStyle.fontSize,
                padding: computedStyle.padding,
                margin: computedStyle.margin
            };
        }

        function measureAllElements() {
            const measurements = {};
            testElements.forEach(elementId => {
                measurements[elementId] = measureElement(elementId);
            });
            
            addTestResult('测量完成', `测量了 ${testElements.length} 个元素`, 'pass');
            return measurements;
        }

        function toggleThemeTest() {
            const beforeMeasurements = measureAllElements();
            
            // 执行主题切换
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(newTheme);
            showThemeIndicator(newTheme);
            
            // 延迟测量以等待过渡完成
            setTimeout(() => {
                const afterMeasurements = measureAllElements();
                const changes = compareResults(beforeMeasurements, afterMeasurements);
                
                if (changes.length === 0) {
                    addTestResult('主题切换测试', '所有元素布局稳定', 'pass');
                } else {
                    addTestResult('主题切换测试', `检测到 ${changes.length} 个元素发生变化`, 'fail');
                    changes.forEach(change => {
                        addTestResult(`  ${change.element}`, change.details, 'warning');
                    });
                }
            }, 400);
        }

        function rapidThemeTest() {
            let count = 0;
            const maxCount = 5;
            
            addTestResult('快速切换测试', '开始5次快速主题切换', 'pass');
            
            const rapidInterval = setInterval(() => {
                toggleThemeTest();
                count++;
                
                if (count >= maxCount) {
                    clearInterval(rapidInterval);
                    addTestResult('快速切换测试', '完成5次快速切换', 'pass');
                }
            }, 800);
        }

        function startContinuousMonitoring() {
            const button = event.target.closest('button');
            const icon = document.getElementById('monitor-icon');
            const text = document.getElementById('monitor-text');
            
            if (!isMonitoring) {
                isMonitoring = true;
                icon.className = 'fas fa-eye-slash';
                text.textContent = '停止连续监控';
                button.classList.remove('warning');
                button.classList.add('success');
                
                monitoringInterval = setInterval(() => {
                    measureAllElements();
                }, 1000);
                
                addTestResult('连续监控', '开始连续监控布局变化', 'pass');
            } else {
                isMonitoring = false;
                icon.className = 'fas fa-eye';
                text.textContent = '开始连续监控';
                button.classList.remove('success');
                button.classList.add('warning');
                
                if (monitoringInterval) {
                    clearInterval(monitoringInterval);
                    monitoringInterval = null;
                }
                
                addTestResult('连续监控', '停止连续监控', 'pass');
            }
        }

        function compareResults(before, after) {
            const changes = [];
            const tolerance = 0.5;

            for (const elementId in before) {
                if (!after[elementId]) continue;
                
                const beforeData = before[elementId];
                const afterData = after[elementId];
                
                ['x', 'y', 'width', 'height'].forEach(prop => {
                    const diff = Math.abs(beforeData[prop] - afterData[prop]);
                    if (diff > tolerance) {
                        changes.push({
                            element: elementId,
                            details: `${prop}: ${beforeData[prop]} → ${afterData[prop]} (差异: ${diff.toFixed(2)}px)`
                        });
                    }
                });
            }

            return changes;
        }

        function applyTheme(theme) {
            currentTheme = theme;
            const darkThemeLink = document.getElementById('dark-theme-style');
            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');

            if (theme === 'dark') {
                document.body.classList.add('dark-theme');
                darkThemeLink.removeAttribute('disabled');
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = '切换到明亮模式';
            } else {
                document.body.classList.remove('dark-theme');
                darkThemeLink.setAttribute('disabled', 'true');
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = '切换到暗黑模式';
            }
        }

        function showThemeIndicator(theme) {
            const indicator = document.getElementById('theme-indicator');
            const icon = indicator.querySelector('i');
            const span = indicator.querySelector('span');
            
            icon.className = `fas fa-${theme === 'dark' ? 'moon' : 'sun'}`;
            span.textContent = `${theme === 'dark' ? '暗黑' : '明亮'}模式`;
            
            indicator.classList.add('visible');
            
            setTimeout(() => {
                indicator.classList.remove('visible');
            }, 2000);
        }

        function addTestResult(label, value, status) {
            const resultsContainer = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            resultItem.innerHTML = `
                <div>
                    <span class="result-label">[${timestamp}] ${label}:</span>
                    <div style="font-size: 10px; color: var(--text-tertiary);">${value}</div>
                </div>
                <span class="result-status ${status}">${status.toUpperCase()}</span>
            `;
            
            resultsContainer.insertBefore(resultItem, resultsContainer.firstChild);
            
            // 限制结果数量
            while (resultsContainer.children.length > 20) {
                resultsContainer.removeChild(resultsContainer.lastChild);
            }
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '<div class="result-item"><span class="result-label">结果已清除</span></div>';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('系统初始化', '布局稳定性测试工具已就绪', 'pass');
            
            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.key === 't' && e.ctrlKey) {
                    e.preventDefault();
                    toggleThemeTest();
                }
                if (e.key === 'm' && e.ctrlKey) {
                    e.preventDefault();
                    measureAllElements();
                }
            });
            
            console.log('布局稳定性验证测试工具已启动');
            console.log('快捷键: Ctrl+T 切换主题, Ctrl+M 测量元素');
        });
    </script>
</body>
</html>
